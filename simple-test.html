<!DOCTYPE html>
<html>
<head>
    <title>Simple Underscore Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Simple Underscore/Lodash Compatibility Test</h1>
    <div id="results"></div>

    <!-- Lo<PERSON>h (simulates the conflict) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
    
    <script>
        // Apply the simple fix
        if (typeof window._ !== 'undefined' && typeof window._.some === 'function') {
            // Simple aliases for missing methods
            if (!window._.contains) window._.contains = window._.includes || function(arr, val) { return arr.indexOf(val) !== -1; };
            if (!window._.any) window._.any = window._.some;
            if (!window._.all) window._.all = window._.every;
            if (!window._.indexOf) window._.indexOf = function(arr, val) { return arr.indexOf(val); };
            if (!window._.each) window._.each = window._.forEach;
            
            // Fix chained methods
            if (window._.chain) {
                var origChain = window._.chain;
                window._.chain = function(obj) {
                    var wrapped = origChain.call(this, obj);
                    if (wrapped) {
                        if (!wrapped.any && wrapped.some) wrapped.any = wrapped.some;
                        if (!wrapped.all && wrapped.every) wrapped.all = wrapped.every;
                        if (!wrapped.contains && wrapped.includes) wrapped.contains = wrapped.includes;
                    }
                    return wrapped;
                };
            }
        }

        function runTests() {
            const results = [];
            const resultsDiv = document.getElementById('results');

            // Test 1: _.contains
            try {
                const test1 = _.contains([1, 2, 3], 2);
                results.push({
                    test: '_.contains',
                    status: test1 ? 'success' : 'error',
                    message: `_.contains([1, 2, 3], 2) = ${test1}`
                });
            } catch (e) {
                results.push({
                    test: '_.contains',
                    status: 'error',
                    message: `Error: ${e.message}`
                });
            }

            // Test 2: _.any
            try {
                const test2 = _.any([1, 2, 3], function(n) { return n > 2; });
                results.push({
                    test: '_.any',
                    status: test2 ? 'success' : 'error',
                    message: `_.any([1, 2, 3], n > 2) = ${test2}`
                });
            } catch (e) {
                results.push({
                    test: '_.any',
                    status: 'error',
                    message: `Error: ${e.message}`
                });
            }

            // Test 3: Chained _.any (the critical test)
            try {
                const test3 = _.chain([1, 2, 3, 4, 5])
                    .map(function(n) { return n * 2; })
                    .any(function(n) { return n > 5; })
                    .value();
                results.push({
                    test: 'Chained _.any',
                    status: test3 ? 'success' : 'error',
                    message: `_.chain([1,2,3,4,5]).map(n*2).any(n>5) = ${test3}`
                });
            } catch (e) {
                results.push({
                    test: 'Chained _.any',
                    status: 'error',
                    message: `Error: ${e.message}`
                });
            }

            // Display results
            resultsDiv.innerHTML = results.map(result => {
                return `<div class="test-result ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`;
            }).join('');

            // Summary
            const successCount = results.filter(r => r.status === 'success').length;
            const totalCount = results.length;
            
            if (successCount === totalCount) {
                resultsDiv.innerHTML += `<div class="test-result success">
                    <strong>✅ All Tests Passed!</strong> The simple fix works correctly.
                </div>`;
            } else {
                resultsDiv.innerHTML += `<div class="test-result error">
                    <strong>❌ Some Tests Failed!</strong> ${successCount}/${totalCount} tests passed.
                </div>`;
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
