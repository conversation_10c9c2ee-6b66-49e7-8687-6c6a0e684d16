<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Underscore.js Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Underscore.js / Lodash Compatibility Fix Test</h1>
    
    <div class="info test-result">
        <strong>Purpose:</strong> This test verifies that the _.contains() method is available and working correctly, 
        which fixes the "_.contains is not a function" error that occurs when Dokan and NotificationX plugins are both active.
    </div>

    <div id="test-results"></div>

    <!-- Simulate loading lodash (which doesn't have _.contains) -->
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>
    
    <script>
        // Apply our fix (same as in the PHP files)
        (function() {
            if (typeof window._ !== 'undefined') {
                // Add _.contains as an alias to _.includes for backward compatibility
                if (typeof window._.contains === 'undefined') {
                    if (typeof window._.includes === 'function') {
                        window._.contains = window._.includes;
                    } else {
                        // Fallback implementation
                        window._.contains = function(array, value) {
                            if (!array) return false;
                            return array.indexOf(value) !== -1;
                        };
                    }
                }

                // Add _.any as an alias to _.some for backward compatibility
                if (typeof window._.any === 'undefined') {
                    if (typeof window._.some === 'function') {
                        window._.any = window._.some;
                    } else {
                        // Fallback implementation
                        window._.any = function(obj, predicate, context) {
                            if (!obj) return false;
                            predicate = predicate || function(value) { return !!value; };
                            if (obj.length === +obj.length) {
                                for (var i = 0, length = obj.length; i < length; i++) {
                                    if (predicate.call(context, obj[i], i, obj)) return true;
                                }
                            } else {
                                var keys = Object.keys(obj);
                                for (var i = 0, length = keys.length; i < length; i++) {
                                    if (predicate.call(context, obj[keys[i]], keys[i], obj)) return true;
                                }
                            }
                            return false;
                        };
                    }
                }

                // Add _.all as an alias to _.every for backward compatibility
                if (typeof window._.all === 'undefined') {
                    if (typeof window._.every === 'function') {
                        window._.all = window._.every;
                    } else {
                        // Fallback implementation
                        window._.all = function(obj, predicate, context) {
                            if (!obj) return true;
                            predicate = predicate || function(value) { return !!value; };
                            if (obj.length === +obj.length) {
                                for (var i = 0, length = obj.length; i < length; i++) {
                                    if (!predicate.call(context, obj[i], i, obj)) return false;
                                }
                            } else {
                                var keys = Object.keys(obj);
                                for (var i = 0, length = keys.length; i < length; i++) {
                                    if (!predicate.call(context, obj[keys[i]], keys[i], obj)) return false;
                                }
                            }
                            return true;
                        };
                    }
                }

                // Fix _.indexOf if missing
                if (typeof window._.indexOf === 'undefined') {
                    window._.indexOf = function(array, value) {
                        if (!array) return -1;
                        return array.indexOf(value);
                    };
                }

                // Fix _.each if missing
                if (typeof window._.each === 'undefined') {
                    window._.each = window._.forEach || function(obj, iterator, context) {
                        if (obj == null) return obj;
                        if (Array.prototype.forEach && obj.forEach === Array.prototype.forEach) {
                            obj.forEach(iterator, context);
                        } else if (obj.length === +obj.length) {
                            for (var i = 0, length = obj.length; i < length; i++) {
                                iterator.call(context, obj[i], i, obj);
                            }
                        } else {
                            var keys = Object.keys(obj);
                            for (var i = 0, length = keys.length; i < length; i++) {
                                iterator.call(context, obj[keys[i]], keys[i], obj);
                            }
                        }
                        return obj;
                    };
                }
            }
        })();

        // Run tests
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let results = [];

            // Test 1: Check if _ is available
            try {
                if (typeof _ !== 'undefined') {
                    results.push({
                        test: 'Underscore/Lodash Library Loaded',
                        status: 'success',
                        message: 'Global _ variable is available'
                    });
                } else {
                    results.push({
                        test: 'Underscore/Lodash Library Loaded',
                        status: 'error',
                        message: 'Global _ variable is not available'
                    });
                }
            } catch (e) {
                results.push({
                    test: 'Underscore/Lodash Library Loaded',
                    status: 'error',
                    message: 'Error checking for _: ' + e.message
                });
            }

            // Test 2: Check if _.contains exists
            try {
                if (typeof _.contains === 'function') {
                    results.push({
                        test: '_.contains Method Available',
                        status: 'success',
                        message: '_.contains is a function'
                    });
                } else {
                    results.push({
                        test: '_.contains Method Available',
                        status: 'error',
                        message: '_.contains is not a function (type: ' + typeof _.contains + ')'
                    });
                }
            } catch (e) {
                results.push({
                    test: '_.contains Method Available',
                    status: 'error',
                    message: 'Error checking _.contains: ' + e.message
                });
            }

            // Test 3: Test _.contains functionality
            try {
                const testArray = [1, 2, 3, 'hello', 'world'];
                const test1 = _.contains(testArray, 2);
                const test2 = _.contains(testArray, 'hello');
                const test3 = _.contains(testArray, 'notfound');
                
                if (test1 === true && test2 === true && test3 === false) {
                    results.push({
                        test: '_.contains Functionality',
                        status: 'success',
                        message: 'All _.contains tests passed: ' + JSON.stringify({test1, test2, test3})
                    });
                } else {
                    results.push({
                        test: '_.contains Functionality',
                        status: 'error',
                        message: 'Some _.contains tests failed: ' + JSON.stringify({test1, test2, test3})
                    });
                }
            } catch (e) {
                results.push({
                    test: '_.contains Functionality',
                    status: 'error',
                    message: 'Error testing _.contains: ' + e.message
                });
            }

            // Test 4: Test _.indexOf functionality
            try {
                const testArray = [1, 2, 3, 'hello', 'world'];
                const index1 = _.indexOf(testArray, 2);
                const index2 = _.indexOf(testArray, 'hello');
                const index3 = _.indexOf(testArray, 'notfound');
                
                if (index1 === 1 && index2 === 3 && index3 === -1) {
                    results.push({
                        test: '_.indexOf Functionality',
                        status: 'success',
                        message: 'All _.indexOf tests passed: ' + JSON.stringify({index1, index2, index3})
                    });
                } else {
                    results.push({
                        test: '_.indexOf Functionality',
                        status: 'error',
                        message: 'Some _.indexOf tests failed: ' + JSON.stringify({index1, index2, index3})
                    });
                }
            } catch (e) {
                results.push({
                    test: '_.indexOf Functionality',
                    status: 'error',
                    message: 'Error testing _.indexOf: ' + e.message
                });
            }

            // Test 5: Test _.each functionality
            try {
                const testArray = [1, 2, 3];
                let sum = 0;
                _.each(testArray, function(value) {
                    sum += value;
                });
                
                if (sum === 6) {
                    results.push({
                        test: '_.each Functionality',
                        status: 'success',
                        message: '_.each test passed: sum = ' + sum
                    });
                } else {
                    results.push({
                        test: '_.each Functionality',
                        status: 'error',
                        message: '_.each test failed: expected sum = 6, got ' + sum
                    });
                }
            } catch (e) {
                results.push({
                    test: '_.each Functionality',
                    status: 'error',
                    message: 'Error testing _.each: ' + e.message
                });
            }

            // Test 6: Test chained methods (critical for media library)
            try {
                // Test _.chain().map().any()
                const chainResult1 = _.chain([1, 2, 3, 4, 5])
                    .map(function(n) { return n * 2; })
                    .any(function(n) { return n > 5; });
                const value1 = chainResult1.value();

                // Test _.chain().map().all()
                const chainResult2 = _.chain([1, 2, 3])
                    .map(function(n) { return n + 1; })
                    .all(function(n) { return n > 1; });
                const value2 = chainResult2.value();

                // Test _.chain().map().contains()
                const chainResult3 = _.chain(['a', 'b', 'c'])
                    .map(function(s) { return s.toUpperCase(); })
                    .contains('B');
                const value3 = chainResult3.value();

                if (value1 === true && value2 === true && value3 === true) {
                    results.push({
                        test: 'Chained Methods (_.chain().map().any/all/contains)',
                        status: 'success',
                        message: 'All chained method tests passed: ' + JSON.stringify({value1, value2, value3})
                    });
                } else {
                    results.push({
                        test: 'Chained Methods (_.chain().map().any/all/contains)',
                        status: 'error',
                        message: 'Some chained method tests failed: ' + JSON.stringify({value1, value2, value3})
                    });
                }
            } catch (e) {
                results.push({
                    test: 'Chained Methods (_.chain().map().any/all/contains)',
                    status: 'error',
                    message: 'Error testing chained methods: ' + e.message
                });
            }

            // Display results
            let html = '<h2>Test Results</h2>';
            results.forEach(result => {
                html += `<div class="test-result ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`;
            });

            // Summary
            const successCount = results.filter(r => r.status === 'success').length;
            const totalCount = results.length;
            
            if (successCount === totalCount) {
                html += `<div class="test-result success">
                    <strong>✅ All Tests Passed!</strong> The underscore.js compatibility fix is working correctly.
                    This should resolve the "_.contains is not a function" error when using Dokan and NotificationX together.
                </div>`;
            } else {
                html += `<div class="test-result error">
                    <strong>❌ Some Tests Failed!</strong> ${successCount}/${totalCount} tests passed.
                    The fix may need additional adjustments.
                </div>`;
            }

            resultsDiv.innerHTML = html;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
