"use strict";(globalThis.webpackChunknotificationx=globalThis.webpackChunknotificationx||[]).push([[5223],{5223:(e,t,l)=>{l.r(t),l.d(t,{default:()=>n});var r=l(6540),o=l(2470);const n=function(e){var t=e.offer_discount,l=e.link_text,n=e.link_button_bg_color,a=e.link_button_text_color,i=e.announcementCSS,c=(0,r.useRef)();return r.createElement("svg",{width:"92",height:"98",viewBox:"0 0 92 98",fill:"none",xmlns:"http://www.w3.org/2000/svg"},r.createElement("path",{d:"M10 0H82V78C82 84.6274 76.6274 90 70 90H22C15.3726 90 10 84.6274 10 78V0Z",fill:(null==i?void 0:i.discountBackground)?null==i?void 0:i.discountBackground:"#4F19CD"}),r.createElement("path",{d:"M82 0L87 5L92 10H82V0Z",fill:(null==i?void 0:i.discountBackground)?null==i?void 0:i.discountBackground:"#806FF6"}),r.createElement("path",{d:"M10 0L5 5L0 10H10V0Z",fill:(null==i?void 0:i.discountBackground)?null==i?void 0:i.discountBackground:"#806FF6"}),r.createElement("g",null,r.createElement("text",{ref:c,xmlSpace:"preserve",style:{whiteSpace:"pre"},fontFamily:"DM Sans",fontSize:"24",fontWeight:"bold",letterSpacing:"0em",fill:(null==i?void 0:i.discountTextColor)?null==i?void 0:i.discountTextColor:"#fff"},r.createElement("tspan",{x:"16",y:"53.548"},t,r.createElement("tspan",{fontSize:"14"},"%")))),r.createElement("g",{filter:"url(#filter1_d_620_42)"},r.createElement("text",{xmlSpace:"preserve",style:{whiteSpace:"pre"},fontFamily:"DM Sans",fontSize:"16",fontWeight:"bold",letterSpacing:"0em",fill:(null==i?void 0:i.discountTextColor)?null==i?void 0:i.discountTextColor:"#fff"},r.createElement("tspan",{x:"37",y:"73.456"},(0,o.__)("OFF","notificationx")))),r.createElement("rect",{x:"13",y:"3",width:"66",height:"17",rx:"2",fill:(null==i?void 0:i.linkButtonBgColor)?null==i?void 0:i.linkButtonBgColor:"#806FF6"}),r.createElement("g",{filter:"url(#filter2_d_620_42)"},r.createElement("text",{fill:(null==i?void 0:i.linkButtonTextColor)?null==i?void 0:i.linkButtonTextColor:"#fff",xmlSpace:"preserve",style:{whiteSpace:"pre",backgroundColor:n,color:a},fontFamily:"DM Sans",fontSize:"10",fontWeight:"500",letterSpacing:"0em"},r.createElement("tspan",{x:"22.709",y:"14"},l))),r.createElement("defs",null,r.createElement("filter",{id:"filter0_d_620_42",x:"21.428",y:"34.064",width:"45.2434",height:"21.272",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:"1"}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),r.createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_620_42"}),r.createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_620_42",result:"shape"})),r.createElement("filter",{id:"filter1_d_620_42",x:"37.72",y:"61.608",width:"29.0688",height:"12.584",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:"1"}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),r.createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_620_42"}),r.createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_620_42",result:"shape"})),r.createElement("filter",{id:"filter2_d_620_42",x:"23.1689",y:"6.8",width:"45.9189",height:"8.31999",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:"1"}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),r.createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_620_42"}),r.createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_620_42",result:"shape"}))))}}}]);